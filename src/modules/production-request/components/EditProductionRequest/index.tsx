import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { useEffect } from "react";
import { useService } from "src/config/context/serviceProvider";
import { getErrorResult } from "src/core/utils/effectErrors";
import { productionRequestOptionsById } from "../../hooks/production-request-options";
import useUpdateProductionRequest from "../../hooks/use-update-production-request";
import type { UpdateProductionRequest } from "../../service/model/production-request";
import { shoppingCartActions } from "../../store/shopping-cart";
import ProductionRequestForm from "../ProductionRequestForm";

interface EditProductionRequestProps {
	id: string;
}

export default function EditProductionRequest({
	id,
}: EditProductionRequestProps) {
	const navigate = useNavigate();
	const service = useService();
	const updateProductionRequestMutation = useUpdateProductionRequest();

	const {
		data: productionRequest,
		isLoading,
		isError,
		error,
	} = useQuery({
		...productionRequestOptionsById(service, id),
		enabled: !!id,
	});

	const handleSubmit = (data: UpdateProductionRequest) => {
		updateProductionRequestMutation.mutate(
			{ ...data, id },
			{
				onSuccess: () => {
					shoppingCartActions.clearCart();
					navigate({ to: "/admin/production-requests" });
				},
			},
		);
	};

	// Load existing items into cart when production request is loaded
	useEffect(() => {
		if (productionRequest?.requests) {
			shoppingCartActions.clearCart();
			// Note: We would need product details to populate the cart properly
			// This is a simplified version - in a real app, you'd fetch product details
			// and populate the cart with the existing items
		}
	}, [productionRequest]);

	// Clear cart when component unmounts
	useEffect(() => {
		return () => {
			shoppingCartActions.clearCart();
		};
	}, []);

	if (isError) {
		return (
			<div className="container mx-auto p-6">
				<div className="alert alert-error">
					<span>Error: {getErrorResult(error).error.message}</span>
				</div>
			</div>
		);
	}

	if (isLoading) {
		return (
			<div className="container mx-auto p-6">
				<div className="flex justify-center py-8">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	if (!productionRequest) {
		return (
			<div className="container mx-auto p-6">
				<div className="alert alert-warning">
					<span>Solicitud de producción no encontrada</span>
				</div>
			</div>
		);
	}

	const defaultValues = {
		code: productionRequest.code,
		clientId: productionRequest.clientId,
		expectedDate: productionRequest.expectedDate || "",
		priority: productionRequest.priority,
		requests: productionRequest.requests.map((item) => ({
			productId: item.productId,
			quantity: item.quantity,
		})),
	};

	return (
		<div className="container mx-auto p-6">
			<div className="mb-6">
				<h1 className="font-bold text-2xl">Editar Solicitud de Producción</h1>
				<p className="text-base-content/70">
					Modifique los datos de la solicitud de producción
				</p>
			</div>

			<ProductionRequestForm
				onSubmit={handleSubmit}
				defaultValues={defaultValues}
				isLoading={updateProductionRequestMutation.isPending}
				submitText="Actualizar Solicitud"
				isEdit={true}
			/>
		</div>
	);
}
