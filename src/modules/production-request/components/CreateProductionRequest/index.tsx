import { useNavigate } from "@tanstack/react-router";
import { useEffect } from "react";
import useCreateProductionRequest from "../../hooks/use-create-production-request";
import type { CreateProductionRequest } from "../../service/model/production-request";
import { shoppingCartActions } from "../../store/shopping-cart";
import ProductionRequestForm from "../ProductionRequestForm";

export default function CreateProductionRequestPage() {
	const navigate = useNavigate();
	const createProductionRequestMutation = useCreateProductionRequest();

	const handleSubmit = (data: CreateProductionRequest) => {
		createProductionRequestMutation.mutate(data, {
			onSuccess: () => {
				shoppingCartActions.clearCart();
				navigate({ to: "/admin/production-requests" });
			},
		});
	};

	// Clear cart when component unmounts
	useEffect(() => {
		return () => {
			shoppingCartActions.clearCart();
		};
	}, []);

	return (
		<div className="container mx-auto p-6">
			<div className="mb-6">
				<h1 className="font-bold text-2xl">Crear Solicitud de Producción</h1>
				<p className="text-base-content/70">
					Complete el formulario para crear una nueva solicitud de producción
				</p>
			</div>

			<ProductionRequestForm
				onSubmit={handleSubmit}
				isLoading={createProductionRequestMutation.isPending}
				submitText="Crear Solicitud"
			/>
		</div>
	);
}
