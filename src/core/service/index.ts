import type {
	CreateArea,
	UpdateArea,
} from "src/modules/area/service/model/area";
import { AreaUsecase } from "src/modules/area/service/model/usecase";
import type {
	CreateProductionRequest,
	UpdateProductionRequest,
} from "src/modules/production-request/service/model/production-request";
import { ProductionRequestUsecase } from "src/modules/production-request/service/model/usecase";
import type { LoginCredentials } from "~/auth/service/model/auth";
import { AuthUsecase } from "~/auth/service/model/usecase";
import type { CreateBrand, UpdateBrand } from "~/brand/service/model/brand";
import { BrandUsecase } from "~/brand/service/model/usecase";
import type {
	BusinessLineCreate,
	BusinessLineUpdate,
} from "~/business-line/service/model/business-line";
import { BusinessLineUsecase } from "~/business-line/service/model/usecase";
import type {
	CategoryCreate,
	CategoryUpdate,
} from "~/category/service/model/category";
import { CategoryUsecase } from "~/category/service/model/usecase";
import type {
	CreateChannel,
	UpdateChannel,
} from "~/channel/service/model/channel";
import { ChannelUsecase } from "~/channel/service/model/usecase";
import type { CreateClient, UpdateClient } from "~/client/service/model/client";
import { ClientUsecase } from "~/client/service/model/usecase";
import type {
	CreateMeasurementUnit,
	UpdateMeasurementUnit,
} from "~/measurement-unit/service/model/measurement-unit";
import { UnitMeasurementCategoryUsecase } from "~/measurement-unit/service/model/unit-measurement-category-usecase";
import { MeasurementUnitUsecase } from "~/measurement-unit/service/model/usecase";
import type {
	CreateOperation,
	UpdateOperation,
} from "~/operation/service/model/operation";
import { OperationUsecase } from "~/operation/service/model/usecase";
import type {
	CreateProduct,
	UpdateProduct,
} from "~/product/service/model/product";
import { ProductUsecase } from "~/product/service/model/usecase";
import type {
	CreateProductionFlow,
	ProductionFlowCreateWithActivities,
	UpdateProductionFlow,
} from "~/production-flow/service/model/production-flow";
import { ProductionFlowUsecase } from "~/production-flow/service/model/usecase";
import type { CreateRecipe, UpdateRecipe } from "~/recipe/service/model/recipe";
import { RecipeUsecase } from "~/recipe/service/model/usecase";
import type { CreateSeller, UpdateSeller } from "~/seller/service/model/seller";
import { SellerUsecase } from "~/seller/service/model/usecase";
import { WarehouseUsecase } from "~/warehouse/service/model/usecase";
import type {
	CreateWarehouse,
	UpdateWarehouse,
} from "~/warehouse/service/model/warehouse";
import { WorkAreaUsecase } from "~/work-area/service/model/usecase";
import type {
	CreateWorkArea,
	UpdateWorkArea,
} from "~/work-area/service/model/work-area";

const authService = {
	login: (credentials: LoginCredentials) => AuthUsecase.login(credentials),
	logout: () => AuthUsecase.logout(),
	getSession: () => AuthUsecase.getSession(),
};

const brandService = {
	create: (brand: CreateBrand) => BrandUsecase.create(brand),
	getAll: () => BrandUsecase.getAll(),
	getById: (id: string) => BrandUsecase.getById(id),
	update: (brand: UpdateBrand) => BrandUsecase.update(brand),
	delete: (id: string) => BrandUsecase.delete(id),
	validateCode: (code: string) => BrandUsecase.validateCode(code),
};

const businessLineService = {
	create: (businessLine: BusinessLineCreate) =>
		BusinessLineUsecase.create(businessLine),
	getAll: () => BusinessLineUsecase.getAll(),
	getById: (id: string) => BusinessLineUsecase.getById(id),
	update: (businessLine: BusinessLineUpdate) =>
		BusinessLineUsecase.update(businessLine),
	delete: (id: string) => BusinessLineUsecase.delete(id),
	getSublines: (id: string) => BusinessLineUsecase.getSublines(id),
	getDetails: (id: string) => BusinessLineUsecase.getDetails(id),
	getParentLines: () => BusinessLineUsecase.getParentLines(),
	getSublinesByCode: (code: string) =>
		BusinessLineUsecase.getSublinesByCode(code),
	getBusinessLineWithDetailsByCode: (code: string) =>
		BusinessLineUsecase.getBusinessLineWithDetailsByCode(code),
	validateCode: (code: string) => BusinessLineUsecase.validateCode(code),
};

const categoryService = {
	create: (category: CategoryCreate) => CategoryUsecase.create(category),
	getAll: () => CategoryUsecase.getAll(),
	getById: (id: string) => CategoryUsecase.getById(id),
	update: (category: CategoryUpdate) => CategoryUsecase.update(category),
	delete: (id: string) => CategoryUsecase.delete(id),
	getSubcategories: (id: string) => CategoryUsecase.getSubcategories(id),
	getDetails: (id: string) => CategoryUsecase.getDetails(id),
	getParents: () => CategoryUsecase.getParents(),
	validateCode: (code: string) => CategoryUsecase.validateCode(code),
};

const channelService = {
	create: (channel: CreateChannel) => ChannelUsecase.create(channel),
	getAll: () => ChannelUsecase.getAll(),
	getById: (id: string) => ChannelUsecase.getById(id),
	update: (channel: UpdateChannel) => ChannelUsecase.update(channel),
	delete: (id: string) => ChannelUsecase.delete(id),
	validateName: (name: string) => ChannelUsecase.validateName(name),
};

const clientService = {
	create: (client: CreateClient) => ClientUsecase.create(client),
	getAll: () => ClientUsecase.getAll(),
	getById: (id: string) => ClientUsecase.getById(id),
	update: (client: UpdateClient) => ClientUsecase.update(client),
	delete: (id: string) => ClientUsecase.delete(id),
	validateDocument: (document: string) =>
		ClientUsecase.validateDocument(document),
};

const measurementUnitService = {
	create: (measurementUnit: CreateMeasurementUnit) =>
		MeasurementUnitUsecase.create(measurementUnit),
	getAll: () => MeasurementUnitUsecase.getAll(),
	getById: (id: string) => MeasurementUnitUsecase.getById(id),
	update: (measurementUnit: UpdateMeasurementUnit) =>
		MeasurementUnitUsecase.update(measurementUnit),
	delete: (id: string) => MeasurementUnitUsecase.delete(id),
	validateCode: (code: string) => MeasurementUnitUsecase.validateCode(code),
};

const unitMeasurementCategoryService = {
	getAll: () => UnitMeasurementCategoryUsecase.getAll(),
};

const operationService = {
	create: (operation: CreateOperation) => OperationUsecase.create(operation),
	getAll: () => OperationUsecase.getAll(),
	getById: (id: string) => OperationUsecase.getById(id),
	update: (operation: UpdateOperation) => OperationUsecase.update(operation),
	delete: (id: string) => OperationUsecase.delete(id),
	validateCode: (code: string) => OperationUsecase.validateCode(code),
};

const workAreaService = {
	create: (workArea: CreateWorkArea) => WorkAreaUsecase.create(workArea),
	getAll: () => WorkAreaUsecase.getAll(),
	getById: (id: string) => WorkAreaUsecase.getById(id),
	update: (workArea: UpdateWorkArea) => WorkAreaUsecase.update(workArea),
	delete: (id: string) => WorkAreaUsecase.delete(id),
	validateCode: (code: string) => WorkAreaUsecase.validateCode(code),
};

const productService = {
	create: (product: CreateProduct) => ProductUsecase.create(product),
	getAll: () => ProductUsecase.getAll(),
	getById: (id: string) => ProductUsecase.getById(id),
	update: (product: UpdateProduct) => ProductUsecase.update(product),
	delete: (id: string) => ProductUsecase.delete(id),
	validateCode: (code: string) => ProductUsecase.validateCode(code),
	validateCommercialName: (commercialName: string) =>
		ProductUsecase.validateCommercialName(commercialName),
	validateSKUCode: (skuCode: string) => ProductUsecase.validateSKUCode(skuCode),
	getProductsByCategoryCode: (categoryCode: string) =>
		ProductUsecase.getProductsByCategoryCode(categoryCode),
};

const productionFlowService = {
	create: (productionFlow: CreateProductionFlow) =>
		ProductionFlowUsecase.create(productionFlow),
	createWithActivities: (productionFlow: ProductionFlowCreateWithActivities) =>
		ProductionFlowUsecase.createWithActivities(productionFlow),
	getAll: () => ProductionFlowUsecase.getAll(),
	getById: (id: string) => ProductionFlowUsecase.getById(id),
	update: (productionFlow: UpdateProductionFlow) =>
		ProductionFlowUsecase.update(productionFlow),
	delete: (id: string) => ProductionFlowUsecase.delete(id),
	getWithActivities: (id: string) =>
		ProductionFlowUsecase.getWithActivities(id),
	validateCode: (code: string) => ProductionFlowUsecase.validateCode(code),
};

const productionRequestService = {
	create: (productionRequest: CreateProductionRequest) =>
		ProductionRequestUsecase.create(productionRequest),
	getAll: () => ProductionRequestUsecase.getAll(),
	getById: (id: string) => ProductionRequestUsecase.getById(id),
	update: (productionRequest: UpdateProductionRequest) =>
		ProductionRequestUsecase.update(productionRequest),
	delete: (id: string) => ProductionRequestUsecase.delete(id),
	validateCode: (code: string) => ProductionRequestUsecase.validateCode(code),
	approve: (id: string) => ProductionRequestUsecase.approve(id),
	reject: (id: string) => ProductionRequestUsecase.reject(id),
};

const recipeService = {
	create: (recipe: CreateRecipe) => RecipeUsecase.create(recipe),
	getAll: () => RecipeUsecase.getAll(),
	getById: (id: string) => RecipeUsecase.getById(id),
	update: (recipe: UpdateRecipe) => RecipeUsecase.update(recipe),
	delete: (id: string) => RecipeUsecase.delete(id),
	validateCode: (code: string) => RecipeUsecase.validateCode(code),
	validateName: (name: string) => RecipeUsecase.validateName(name),
};

const sellerService = {
	create: (seller: CreateSeller) => SellerUsecase.create(seller),
	getAll: () => SellerUsecase.getAll(),
	getById: (id: string) => SellerUsecase.getById(id),
	update: (seller: UpdateSeller) => SellerUsecase.update(seller),
	delete: (id: string) => SellerUsecase.delete(id),
	validateIdentityDocument: (identityDocument: string) =>
		SellerUsecase.validateIdentityDocument(identityDocument),
};

const warehouseService = {
	create: (warehouse: CreateWarehouse) => WarehouseUsecase.create(warehouse),
	getAll: () => WarehouseUsecase.getAll(),
	getById: (id: string) => WarehouseUsecase.getById(id),
	update: (warehouse: UpdateWarehouse) => WarehouseUsecase.update(warehouse),
	delete: (id: string) => WarehouseUsecase.delete(id),
	validateCode: (code: string) => WarehouseUsecase.validateCode(code),
	validateName: (name: string) => WarehouseUsecase.validateName(name),
};

const areaService = {
	getAll: () => AreaUsecase.getAll(),
	getById: (id: string) => AreaUsecase.getById(id),
	create: (area: CreateArea) => AreaUsecase.create(area),
	update: (area: UpdateArea) => AreaUsecase.update(area),
	delete: (id: string) => AreaUsecase.delete(id),
	validateCode: (code: string) => AreaUsecase.validateCode(code),
};

export const serviceRegistry = {
	auth: authService,
	area: areaService,
	brand: brandService,
	businessLine: businessLineService,
	category: categoryService,
	channel: channelService,
	client: clientService,
	measurementUnit: measurementUnitService,
	unitMeasurementCategory: unitMeasurementCategoryService,
	operation: operationService,
	product: productService,
	recipe: recipeService,
	seller: sellerService,
	warehouse: warehouseService,
	workArea: workAreaService,
	productionFlow: productionFlowService,
	productionRequest: productionRequestService,
};

export type serviceRegistry = typeof serviceRegistry;
